package com.hakcu.evmodbus.controllers;

import com.hakcu.evmodbus.entities.Spot;
import com.hakcu.evmodbus.services.StatisticsBusinessService;
import com.hakcu.evmodbus.services.StatisticsService;
import com.hakcu.evmodbus.dto.ChartConfiguration;
import com.hakcu.evmodbus.dto.TimePeriodData;
import com.hakcu.evmodbus.services.UnifiedPdfReportService;
import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.ByteArrayOutputStream;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map;

/**
 * Controller for the statistics page.
 * Handles requests for viewing statistics about spots and readings.
 */
@Controller
@RequestMapping("/statistics")
public class StatisticsController {

    private static final Logger logger = LoggerFactory.getLogger(StatisticsController.class);
    private final StatisticsService statisticsService;
    private final StatisticsBusinessService statisticsBusinessService;
    private final UnifiedPdfReportService unifiedPdfReportService;

    public StatisticsController(StatisticsService statisticsService,
                              StatisticsBusinessService statisticsBusinessService,
                                UnifiedPdfReportService unifiedPdfReportService) {
        this.statisticsService = statisticsService;
        this.statisticsBusinessService = statisticsBusinessService;
        this.unifiedPdfReportService = unifiedPdfReportService;
    }

    /**
     * Displays the statistics page with three graphs: hourly, daily, and monthly.
     *
     * @param spotId        The ID of the spot to show statistics for.
     * @param selectedDay   The day to show hourly statistics for.
     * @param selectedMonth The month to show daily statistics for.
     * @param selectedYear  The year to show monthly statistics for.
     * @param model         The model to add attributes to.
     * @return The name of the view to render.
     */
    @GetMapping
    public String showStatistics(
            @RequestParam(required = false) Long spotId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate selectedDay,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM") String selectedMonth,
            @RequestParam(required = false) Integer selectedYear,
            @RequestParam(required = false, defaultValue = "hourly") String activeTab,
            Model model,
            HttpServletRequest request) {

        // Process time period parameters with defaults
        TimePeriodData timePeriod = TimePeriodData.withDefaults(selectedDay, selectedMonth, selectedYear);

        // Add common model attributes
        addCommonModelAttributes(model, spotId, timePeriod, activeTab);

        // If a spot is selected, generate statistics
        if (spotId != null) {
            processSpotStatistics(model, spotId, timePeriod, request);
        }

        return "statistics/list";
    }

    /**
     * Handles tab switching via HTMX.
     * Returns the filter container HTML with the appropriate filter visible.
     *
     * @param tabId         The ID of the tab to switch to.
     * @param spotId        The ID of the selected spot.
     * @param selectedDay   The selected day.
     * @param selectedMonth The selected month.
     * @param selectedYear  The selected year.
     * @param model         The model to add attributes to.
     * @return The filter container HTML fragment.
     */
    @GetMapping("/tab-switch")
    public String switchTab(
            @RequestParam String tabId,
            @RequestParam(required = false) Long spotId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate selectedDay,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM") String selectedMonth,
            @RequestParam(required = false) Integer selectedYear,
            Model model) {

        // Process time period parameters with defaults
        TimePeriodData timePeriod = TimePeriodData.withDefaults(selectedDay, selectedMonth, selectedYear);

        // Add common model attributes
        addCommonModelAttributes(model, spotId, timePeriod, tabId);

        // Add current period flag for the selected tab
        addCurrentPeriodFlag(model, tabId, timePeriod);

        return "statistics/fragments/filter-container :: filter-container";
    }

    /**
     * Handles navigation between time periods (previous/next day, month, or year).
     * Returns the full page with updated data.
     *
     * @param spotId        The ID of the selected spot.
     * @param activeTab     The active tab (hourly, daily, monthly).
     * @param direction     The navigation direction (prev, next).
     * @param selectedDay   The currently selected day.
     * @param selectedMonth The currently selected month.
     * @param selectedYear  The currently selected year.
     * @param model         The model to add attributes to.
     * @return The full statistics page with updated data.
     */
    @GetMapping("/navigate")
    public String navigateTimePeriod(
            @RequestParam(required = false) Long spotId,
            @RequestParam(required = false, defaultValue = "hourly") String activeTab,
            @RequestParam String direction,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate selectedDay,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM") String selectedMonth,
            @RequestParam(required = false) Integer selectedYear,
            Model model,
            HttpServletRequest request) {

        // Process time period parameters with defaults
        TimePeriodData timePeriod = TimePeriodData.withDefaults(selectedDay, selectedMonth, selectedYear);

        // Calculate new date values based on navigation
        TimePeriodData navigatedPeriod = calculateNavigatedPeriod(timePeriod, activeTab, direction);

        // Forward to the main statistics method with the updated parameters
        return showStatistics(spotId, navigatedPeriod.selectedDay(), navigatedPeriod.selectedMonth(),
            navigatedPeriod.selectedYear(), activeTab, model, request);
    }

    /**
     * Downloads a unified PDF report for the selected spot and time period.
     * Uses the new unified PDF generation system for consistent, professional reports.
     *
     * @param spotId        The ID of the spot.
     * @param reportType    The type of report (day, month or year).
     * @param selectedDay   The day for daily reports.
     * @param selectedMonth The month for monthly reports.
     * @param selectedYear  The year for yearly reports.
     * @return ResponseEntity with the PDF file.
     */
    @GetMapping("/pdf")
    public ResponseEntity<byte[]> downloadPdf(
            @RequestParam Long spotId,
            @RequestParam String reportType,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate selectedDay,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM") String selectedMonth,
            @RequestParam(required = false) Integer selectedYear) {

        try {
            // Get spot information for filename generation
            Spot spot = statisticsService.getSpotById(spotId)
                    .orElseThrow(() -> new EntityNotFoundException("Spot not found"));

            // Generate unified PDF report
            ByteArrayOutputStream pdfOutput = unifiedPdfReportService.generateUnifiedReport(
                    spotId, reportType, selectedDay, selectedMonth, selectedYear);

            // Generate professional filename based on report type
            String filename = generatePdfFilename(spot, reportType, selectedDay, selectedMonth, selectedYear);

            // Return the PDF as a downloadable file
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_PDF)
                    .body(pdfOutput.toByteArray());

        } catch (Exception e) {
            logger.error("Error generating unified PDF report for spot {} and report type {}: {}", spotId, reportType, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Generates a professional filename for the PDF report.
     */
    private String generatePdfFilename(Spot spot, String reportType, LocalDate selectedDay,
                                     String selectedMonth, Integer selectedYear) {
        LocalDate today = LocalDate.now();
        YearMonth currentMonth = YearMonth.now();
        int currentYear = LocalDate.now().getYear();

        return switch (reportType.toLowerCase()) {
            case "day" -> {
                LocalDate day = (selectedDay != null) ? selectedDay : today;
                boolean isCurrentDay = day.equals(today);
                String formattedDay = day.format(DateTimeFormatter.ofPattern("dd_MM_yyyy"));
                String currentTimeSuffix = isCurrentDay ? "_hasta_" + LocalTime.now().format(DateTimeFormatter.ofPattern("HH_mm")) : "";

                yield String.format("informe_diario_plaza_%d_sotano_%d_%s%s.pdf",
                        spot.getSpotNumber(), spot.getFloor().getFloorNumber(),
                        formattedDay, currentTimeSuffix);
            }
            case "month" -> {
                YearMonth yearMonth = (selectedMonth != null) ? YearMonth.parse(selectedMonth) : currentMonth;
                boolean isCurrentMonth = yearMonth.equals(currentMonth);
                String monthName = yearMonth.getMonth().getDisplayName(TextStyle.FULL, Locale.of("es", "ES"));
                String currentTimeSuffix = isCurrentMonth ? "_hasta_" + today.format(DateTimeFormatter.ofPattern("dd_MM")) : "";

                yield String.format("informe_mensual_plaza_%d_sotano_%d_%s_%d%s.pdf",
                        spot.getSpotNumber(), spot.getFloor().getFloorNumber(),
                        monthName, yearMonth.getYear(), currentTimeSuffix);
            }
            case "year" -> {
                int year = (selectedYear != null) ? selectedYear : currentYear;
                boolean isCurrentYear = year == currentYear;
                String currentTimeSuffix = isCurrentYear ? "_hasta_" + currentMonth.getMonth().getDisplayName(TextStyle.SHORT, Locale.of("es", "ES")) : "";

                yield String.format("informe_anual_plaza_%d_sotano_%d_%d%s.pdf",
                        spot.getSpotNumber(), spot.getFloor().getFloorNumber(), year, currentTimeSuffix);
            }
            default -> {
                // Default to current day
                String formattedDay = today.format(DateTimeFormatter.ofPattern("dd_MM_yyyy"));
                String currentTimeSuffix = "_hasta_" + LocalTime.now().format(DateTimeFormatter.ofPattern("HH_mm"));

                yield String.format("informe_diario_plaza_%d_sotano_%d_%s%s.pdf",
                        spot.getSpotNumber(), spot.getFloor().getFloorNumber(),
                        formattedDay, currentTimeSuffix);
            }
        };
    }

    /**
     * Adds common model attributes used across multiple endpoints.
     */
    private void addCommonModelAttributes(Model model, Long spotId, TimePeriodData timePeriod, String activeTab) {
        model.addAttribute("spots", statisticsService.getAllSpots());
        model.addAttribute("selectedSpotId", spotId);
        model.addAttribute("selectedDay", timePeriod.selectedDay());
        model.addAttribute("selectedMonth", timePeriod.selectedMonth());
        model.addAttribute("selectedYear", timePeriod.selectedYear());
        model.addAttribute("activeTab", activeTab);
    }

    /**
     * Processes spot statistics including charts and consumption data.
     */
    private void processSpotStatistics(Model model, Long spotId, TimePeriodData timePeriod, HttpServletRequest request) {
        // Get spot and add to model
        Spot spot = statisticsBusinessService.getSpotById(spotId);
        model.addAttribute("spot", spot);

        // Check if spot has any readings
        boolean hasAnyReadings = statisticsBusinessService.hasAnyReadings(spotId);
        model.addAttribute("hasAnyReadings", hasAnyReadings);

        if (!hasAnyReadings) {
            model.addAttribute("noDataMessage", "No hay datos de consumo disponibles para esta plaza.");
        }

        // Create chart configuration
        ChartConfiguration chartConfig = ChartConfiguration.forDevice(isMobileDevice(request));

        // Generate all charts
        statisticsBusinessService.generateHourlyChart(model, spotId, timePeriod, chartConfig);
        statisticsBusinessService.generateDailyChart(model, spotId, timePeriod, chartConfig);
        statisticsBusinessService.generateMonthlyChart(model, spotId, timePeriod, chartConfig);

        // Add combined data for backward compatibility
        addCombinedTimeSeriesData(model);

        // Add current period consumption
        addCurrentPeriodConsumption(model, spotId);

        // Add date range attributes for readings links
        statisticsBusinessService.addDateRangeAttributes(model, timePeriod);
    }

    /**
     * Adds combined time series data for backward compatibility.
     */
    private void addCombinedTimeSeriesData(Model model) {
        Map<String, Float> combinedData = new LinkedHashMap<>();
        combinedData.putAll((Map<String, Float>) model.getAttribute("hourlyData"));
        combinedData.putAll((Map<String, Float>) model.getAttribute("dailyData"));
        combinedData.putAll((Map<String, Float>) model.getAttribute("monthlyData"));
        model.addAttribute("timeSeriesData", combinedData);
    }

    /**
     * Adds current period consumption data to the model.
     */
    private void addCurrentPeriodConsumption(Model model, Long spotId) {
        Map<String, Float> consumption = statisticsBusinessService.calculateCurrentPeriodConsumption(spotId);
        model.addAttribute("todayConsumption", consumption.get("today"));
        model.addAttribute("thisMonthConsumption", consumption.get("thisMonth"));
        model.addAttribute("thisYearConsumption", consumption.get("thisYear"));
    }

    /**
     * Adds current period flag for the selected tab.
     */
    private void addCurrentPeriodFlag(Model model, String tabId, TimePeriodData timePeriod) {
        switch (tabId) {
            case "hourly" -> model.addAttribute("isCurrentDay", timePeriod.isCurrentDay());
            case "daily" -> model.addAttribute("isCurrentMonth", timePeriod.isCurrentMonth());
            case "monthly" -> model.addAttribute("isCurrentYear", timePeriod.isCurrentYear());
        }
    }

    /**
     * Calculates navigated time period based on direction and active tab.
     */
    private TimePeriodData calculateNavigatedPeriod(TimePeriodData timePeriod, String activeTab, String direction) {
        LocalDate today = LocalDate.now();
        LocalDate newDay = timePeriod.selectedDay();
        String newMonth = timePeriod.selectedMonth();
        Integer newYear = timePeriod.selectedYear();

        switch (activeTab) {
            case "hourly" -> {
                if ("prev".equals(direction)) {
                    newDay = timePeriod.selectedDay().minusDays(1);
                } else if ("next".equals(direction)) {
                    LocalDate nextDay = timePeriod.selectedDay().plusDays(1);
                    if (!nextDay.isAfter(today)) {
                        newDay = nextDay;
                    }
                }
            }
            case "daily" -> {
                YearMonth currentYearMonth = timePeriod.getYearMonth();
                if ("prev".equals(direction)) {
                    newMonth = currentYearMonth.minusMonths(1).toString();
                } else if ("next".equals(direction)) {
                    YearMonth nextMonth = currentYearMonth.plusMonths(1);
                    if (!nextMonth.isAfter(YearMonth.now())) {
                        newMonth = nextMonth.toString();
                    }
                }
            }
            case "monthly" -> {
                if ("prev".equals(direction)) {
                    newYear = timePeriod.selectedYear() - 1;
                } else if ("next".equals(direction)) {
                    int nextYear = timePeriod.selectedYear() + 1;
                    if (nextYear <= today.getYear()) {
                        newYear = nextYear;
                    }
                }
            }
        }

        return TimePeriodData.withDefaults(newDay, newMonth, newYear);
    }

    /**
     * Determines if the request is coming from a mobile device based on the User-Agent header.
     *
     * @param request The HTTP request.
     * @return True if the request is from a mobile device, false otherwise.
     */
    private boolean isMobileDevice(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent").toLowerCase();
        return userAgent.contains("mobile") || userAgent.contains("android") || userAgent.contains("iphone") ||
                userAgent.contains("ipad") || userAgent.contains("ipod") || userAgent.contains("blackberry") ||
                userAgent.contains("windows phone");
    }
}
